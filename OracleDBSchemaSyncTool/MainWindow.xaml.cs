using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using OracleDBSchemaSyncTool.Models;
using OracleDBSchemaSyncTool.Services;

namespace OracleDBSchemaSyncTool
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly SchemaService _schemaService;
        private readonly ComparisonService _comparisonService;
        private List<SchemaObject> _comparisonResults = new();

        public MainWindow()
        {
            InitializeComponent();

            _schemaService = new SchemaService();
            _comparisonService = new ComparisonService();
        }

        private async void TestSourceConnection_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(GetSourceConnection(), "Source");
        }

        private async void TestTargetConnection_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(GetTargetConnection(), "Target");
        }

        private async Task TestConnection(DatabaseConnection connection, string connectionName)
        {
            StatusText.Text = $"Testing {connectionName} connection...";

            try
            {
                var isConnected = await connection.TestConnectionAsync();
                MessageBox.Show(
                    isConnected ? $"{connectionName} connection successful!" : $"{connectionName} connection failed!",
                    "Connection Test",
                    MessageBoxButton.OK,
                    isConnected ? MessageBoxImage.Information : MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Connection error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                StatusText.Text = "Ready";
            }
        }

        private async void CompareSchemas_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "Comparing schemas...";

            try
            {
                var sourceConnection = GetSourceConnection();
                var targetConnection = GetTargetConnection();

                var sourceObjects = await _schemaService.GetSchemaObjectsAsync(sourceConnection, sourceConnection.Username.ToUpper());
                var targetObjects = await _schemaService.GetSchemaObjectsAsync(targetConnection, targetConnection.Username.ToUpper());

                _comparisonResults = _comparisonService.CompareSchemas(sourceObjects, targetObjects);
                ComparisonGrid.ItemsSource = _comparisonResults;

                GenerateScript.IsEnabled = _comparisonResults.Any();
                StatusText.Text = $"Comparison complete. Found {_comparisonResults.Count} differences.";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Comparison error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "Comparison failed";
            }
        }

        private void GenerateScript_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var scriptService = new ScriptGenerationService();
                var script = scriptService.GenerateScript(_comparisonResults,
                    IncludeDropStatements.IsChecked ?? false,
                    IncludeCreateStatements.IsChecked ?? false,
                    IncludeAlterStatements.IsChecked ?? false);

                SyncScriptTextBox.Text = script;
                ScriptStatsText.Text = $"Generated {_comparisonResults.Count} statements";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Script generation error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (ComparisonGrid.ItemsSource != null)
            {
                var filterText = FilterTextBox.Text?.ToLower() ?? string.Empty;
                var view = System.Windows.Data.CollectionViewSource.GetDefaultView(ComparisonGrid.ItemsSource);

                if (string.IsNullOrEmpty(filterText))
                {
                    view.Filter = null;
                }
                else
                {
                    view.Filter = (obj) =>
                    {
                        if (obj is SchemaObject schemaObj)
                        {
                            return schemaObj.Name.ToLower().Contains(filterText) ||
                                   schemaObj.Type.ToLower().Contains(filterText) ||
                                   (schemaObj.Differences?.ToLower().Contains(filterText) ?? false);
                        }
                        return false;
                    };
                }
            }
        }

        private void SaveScript_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "SQL Files (*.sql)|*.sql|Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                    DefaultExt = "sql",
                    FileName = $"SchemaSync_{DateTime.Now:yyyyMMdd_HHmmss}.sql"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    File.WriteAllText(saveFileDialog.FileName, SyncScriptTextBox.Text);
                    MessageBox.Show("Script saved successfully!", "Save Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving script: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private DatabaseConnection GetSourceConnection()
        {
            return new DatabaseConnection
            {
                Server = SourceServer.Text,
                Port = int.TryParse(SourcePort.Text, out var port) ? port : 1521,
                ServiceName = SourceServiceName.Text,
                Username = SourceUsername.Text,
                Password = SourcePassword.Password,
                ConnectionName = "Source"
            };
        }

        private DatabaseConnection GetTargetConnection()
        {
            return new DatabaseConnection
            {
                Server = TargetServer.Text,
                Port = int.TryParse(TargetPort.Text, out var port) ? port : 1521,
                ServiceName = TargetServiceName.Text,
                Username = TargetUsername.Text,
                Password = TargetPassword.Password,
                ConnectionName = "Target"
            };
        }
    }
}