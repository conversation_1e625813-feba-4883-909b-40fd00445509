<Window x:Class="OracleDBSchemaSyncTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
        Title="Oracle DB Schema Sync Tool" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="{DynamicResource PrimaryBrush}" Padding="15">
                <TextBlock Text="Oracle Database Schema Comparison Tool"
                          FontSize="20" FontWeight="Bold"
                          Foreground="White" HorizontalAlignment="Center"/>
            </Border>

            <!-- Main Content -->
            <TabControl Grid.Row="1" Margin="10">

                <!-- Connection Tab -->
                <TabItem Header="Database Connections">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Source Database Connection -->
                        <GroupBox Grid.Column="0" Header="Source Database" Margin="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="SourceServer" Grid.Row="0" Grid.Column="1"
                                        Margin="5" Text="localhost"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="SourcePort" Grid.Row="1" Grid.Column="1"
                                        Margin="5" Text="1521"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Service Name:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="SourceServiceName" Grid.Row="2" Grid.Column="1"
                                        Margin="5" Text="XE"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="SourceUsername" Grid.Row="3" Grid.Column="1"
                                        Margin="5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="5"/>
                                <PasswordBox x:Name="SourcePassword" Grid.Row="4" Grid.Column="1"
                                           Margin="5"/>

                                <Button x:Name="TestSourceConnection" Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2"
                                       Content="Test Connection" Margin="5" Padding="10,5"
                                       Click="TestSourceConnection_Click"/>
                            </Grid>
                        </GroupBox>

                        <!-- Target Database Connection -->
                        <GroupBox Grid.Column="1" Header="Target Database" Margin="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="TargetServer" Grid.Row="0" Grid.Column="1"
                                        Margin="5" Text="localhost"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="TargetPort" Grid.Row="1" Grid.Column="1"
                                        Margin="5" Text="1521"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Service Name:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="TargetServiceName" Grid.Row="2" Grid.Column="1"
                                        Margin="5" Text="XE"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="5"/>
                                <TextBox x:Name="TargetUsername" Grid.Row="3" Grid.Column="1"
                                        Margin="5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="5"/>
                                <PasswordBox x:Name="TargetPassword" Grid.Row="4" Grid.Column="1"
                                           Margin="5"/>

                                <Button x:Name="TestTargetConnection" Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2"
                                       Content="Test Connection" Margin="5" Padding="10,5"
                                       Click="TestTargetConnection_Click"/>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </TabItem>
                <!-- Schema Comparison Tab -->
                <TabItem Header="Schema Comparison">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Comparison Controls -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" HorizontalAlignment="Center">
                            <Button x:Name="CompareSchemas" Content="Compare Schemas"
                                   Padding="15,8" Margin="5" FontSize="14" FontWeight="Bold"
                                   Click="CompareSchemas_Click"/>
                            <Button x:Name="GenerateScript" Content="Generate Sync Script"
                                   Padding="15,8" Margin="5" FontSize="14"
                                   IsEnabled="False" Click="GenerateScript_Click"/>
                            <TextBox x:Name="FilterTextBox"
                                    Width="200" Margin="20,5,5,5"
                                    TextChanged="FilterTextBox_TextChanged"
                                    ToolTip="Filter results..."/>
                        </StackPanel>

                        <!-- Results Grid -->
                        <DataGrid x:Name="ComparisonGrid" Grid.Row="1"
                                  AutoGenerateColumns="False" Margin="10"
                                  CanUserSortColumns="True" CanUserReorderColumns="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Object Name" Binding="{Binding Name}" Width="200"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="100"/>
                                <DataGridTextColumn Header="Owner" Binding="{Binding Owner}" Width="100"/>
                                <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100">
                                    <DataGridTextColumn.CellStyle>
                                        <Style TargetType="DataGridCell">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="Added">
                                                    <Setter Property="Background" Value="LightGreen"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="Removed">
                                                    <Setter Property="Background" Value="LightCoral"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="Modified">
                                                    <Setter Property="Background" Value="LightYellow"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGridTextColumn.CellStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="Differences" Binding="{Binding Differences}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>

                <!-- Script Generation Tab -->
                <TabItem Header="Generated Script">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Script Controls -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                            <CheckBox x:Name="IncludeDropStatements" Content="Include DROP statements"
                                     IsChecked="True" Margin="5" VerticalAlignment="Center"/>
                            <CheckBox x:Name="IncludeCreateStatements" Content="Include CREATE statements"
                                     IsChecked="True" Margin="5" VerticalAlignment="Center"/>
                            <CheckBox x:Name="IncludeAlterStatements" Content="Include ALTER statements"
                                     IsChecked="True" Margin="5" VerticalAlignment="Center"/>
                            <Button x:Name="SaveScript" Content="Save Script"
                                   Padding="10,5" Margin="20,5,5,5"
                                   Click="SaveScript_Click"/>
                        </StackPanel>

                        <!-- Script Text -->
                        <TextBox x:Name="SyncScriptTextBox" Grid.Row="1"
                                Margin="10" IsReadOnly="True"
                                AcceptsReturn="True" TextWrapping="Wrap"
                                VerticalScrollBarVisibility="Auto"
                                HorizontalScrollBarVisibility="Auto"
                                FontFamily="Consolas" FontSize="12"/>

                        <!-- Script Statistics -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="10">
                            <TextBlock x:Name="ScriptStatsText" Text="Script ready for execution"
                                      VerticalAlignment="Center" Margin="5"/>
                        </StackPanel>
                    </Grid>
                </TabItem>

            </TabControl>

            <!-- Status Bar -->
            <Border Grid.Row="2" Background="{DynamicResource BorderBrush}" Padding="10,5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="StatusText" Grid.Column="0" Text="Ready"
                              VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="Version 1.0" VerticalAlignment="Center" Margin="10,0"/>
                    </StackPanel>
                </Grid>
            </Border>

    </Grid>
</Window>
